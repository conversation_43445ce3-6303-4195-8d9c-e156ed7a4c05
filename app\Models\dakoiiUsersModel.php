<?php

namespace App\Models;

use CodeIgniter\Model;

d protected $useSoftDeletes   = false;

    // Fields matching the current database structure
    protected $allowedFields = [
        'name',
        'username',
        'password',
        'role',
        'is_active'
    ];Fmatchgcurrent taba stucur

    // Enable timestamps
    protected $useTimestamps = true;
    protected $crea    protected $updatedField  = 'updated_at';
    protected $deletedField  = '';

    // Validation rules
    protected $validationRules = [
        'name' => 'required|min_length[2]|max_length[255]',
        'username' => 'required|min_length[3]|max_length[255]|is_unique[dakoii_users.username,id,{id}]',
        'password' => 'required|min_length[6]',
        'role' => 'required|in_list[user,moderator,admin]',
        'is_active' => 'in_list[0,1]'
    ];

    protected $validationMessages = [];
    protected $ski
ntion hashPassword(array $data)
    /**e user for Dakoii portal
     */lnnticats$userna|is[umd$ar

        unset($user['password']);
        return $user;
    }

    /**
     * Get active users only
     */
    public function getActiveUsers()
    {
        return $this->where('is_active', 1)->findAll();
    }

    /**
     * Get users by role
     */
    public function getUsersByRole(string $role)
    {
        return $this->where('role', $role)->findAll();
    }

    /**
     * Get active users by rolertwh
                   ->where('is_active', 1)
                   ->findAll();
    }
   
    public function usernameExists(string $username, int $excludeId = null): bool
    {
        $builder = $this->where('username', $username);
eId) {
            $builder->where('id !=', $excludeId);
        }
urn $builder->countAllR

    /**     * Get user statistics for dashboard

    public function getUserStats(): array
        return [
         ole', 'admin')->countAllResults(),
            'moderator_users' => $this->where('role', 'moderator')->countAllResults(),
     _>iweus-    /**
     * Get user with safe data (without password)
     */
    public function getSafeUser(int $userId): array|null
    {
        $user = $this->find($userId);
        if ($user) {
            unset($user['password']);
        }
        return $user;
    }

    /**
     * Search users by name or username
     */
    public function searchUsers(string $searchTerm): array
    {
        return $this->groupStart()
                   ->like('name', $searchTerm)
                   ->orLike('username', $searchTerm)
                   ->groupEnd()
                   ->findAll();
    }
}
moertrmoertruu