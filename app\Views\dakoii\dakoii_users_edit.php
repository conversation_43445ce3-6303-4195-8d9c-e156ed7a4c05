<?= $this->extend("templates/dakoii_template"); ?>
<?= $this->section('content'); ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0">Edit User</h2>
        <p class="text-muted mb-0">Update user information and permissions</p>
    </div>
    <div>
        <a href="<?= base_url('dakoii/users') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Users
        </a>
        <a href="<?= base_url('dakoii/users/show/' . $user['id']) ?>" class="btn btn-info">
            <i class="fas fa-eye"></i> View Details
        </a>
    </div>
</div>

<!-- User Information Card -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-edit"></i> Edit User Information
                </h5>
            </div>
            <div class="card-body">
                <?= form_open('dakoii/users/update/' . $user['id'], ['class' => 'needs-validation', 'novalidate' => true]) ?>
                    <?= csrf_field() ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?= old('name', esc($user['name'])) ?>" required>
                                <div class="invalid-feedback">
                                    Please provide a valid name.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?= old('username', esc($user['username'])) ?>" required>
                                <div class="invalid-feedback">
                                    Please provide a valid username.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="Leave blank to keep current password">
                                <div class="form-text">
                                    <i class="fas fa-info-circle"></i> Leave blank to keep current password
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="">Select Role</option>
                                    <option value="user" <?= old('role', $user['role']) == 'user' ? 'selected' : '' ?>>User</option>
                                    <option value="moderator" <?= old('role', $user['role']) == 'moderator' ? 'selected' : '' ?>>Moderator</option>
                                    <option value="admin" <?= old('role', $user['role']) == 'admin' ? 'selected' : '' ?>>Administrator</option>
                                </select>
                                <div class="invalid-feedback">
                                    Please select a valid role.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="orgcode" class="form-label">Organization <span class="text-danger">*</span></label>
                                <select class="form-select" id="orgcode" name="orgcode" required>
                                    <option value="">Select Organization</option>
                                    <?php foreach ($organizations as $org): ?>
                                        <option value="<?= esc($org['orgcode']) ?>" 
                                                <?= old('orgcode', $user['orgcode']) == $org['orgcode'] ? 'selected' : '' ?>>
                                            <?= esc($org['name']) ?> (<?= esc($org['orgcode']) ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">
                                    Please select an organization.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="is_active" class="form-label">Status</label>
                                <div class="form-check form-switch mt-2">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           value="1" <?= old('is_active', $user['is_active']) ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="is_active">
                                        Active User
                                    </label>
                                </div>
                                <div class="form-text">
                                    <i class="fas fa-info-circle"></i> Inactive users cannot login to the system
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <a href="<?= base_url('dakoii/users') ?>" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update User
                        </button>
                    </div>
                <?= form_close() ?>
            </div>
        </div>
    </div>

    <!-- User Summary Card -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> User Summary
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center" 
                         style="width: 80px; height: 80px;">
                        <span class="text-white fw-bold fs-2">
                            <?= strtoupper(substr($user['name'], 0, 1)) ?>
                        </span>
                    </div>
                    <h5 class="mt-2 mb-0"><?= esc($user['name']) ?></h5>
                    <small class="text-muted">@<?= esc($user['username']) ?></small>
                </div>

                <div class="mb-3">
                    <strong>Current Role:</strong><br>
                    <span class="badge bg-<?= $user['role'] == 'admin' ? 'danger' : ($user['role'] == 'moderator' ? 'warning' : 'info') ?>">
                        <i class="fas fa-<?= $user['role'] == 'admin' ? 'user-shield' : ($user['role'] == 'moderator' ? 'user-cog' : 'user') ?>"></i>
                        <?= ucfirst($user['role']) ?>
                    </span>
                </div>

                <div class="mb-3">
                    <strong>Current Status:</strong><br>
                    <span class="badge bg-<?= $user['is_active'] ? 'success' : 'danger' ?>">
                        <i class="fas fa-<?= $user['is_active'] ? 'check' : 'times' ?>"></i>
                        <?= $user['is_active'] ? 'Active' : 'Inactive' ?>
                    </span>
                </div>

                <div class="mb-3">
                    <strong>User ID:</strong><br>
                    <code class="bg-light px-2 py-1 rounded">#<?= $user['id'] ?></code>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-lightbulb"></i>
                    <strong>Tip:</strong> Changes will take effect immediately after saving.
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-check-input:checked {
    background-color: #198754;
    border-color: #198754;
}

.was-validated .form-control:valid,
.was-validated .form-select:valid {
    border-color: #198754;
}

.was-validated .form-control:invalid,
.was-validated .form-select:invalid {
    border-color: #dc3545;
}
</style>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?= $this->endSection() ?>
