CRITICAL - 2025-06-13 08:28:49 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'orgcode' in 'where clause'
[Method: GET, Route: dakoii/organizations/show/2345]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `users`
WHERE CAST(orgcode AS CHAR) = :CAST(orgcode AS CHAR):
AND `role` = :role:', [...], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Controllers\DakoiiOrganizations.php(165): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiOrganizations->show('2345')
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiOrganizations))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-13 08:28:49 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'orgcode' in 'where clause'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `users`
WHERE CAST(orgcode AS CHAR) = \'2345\'
AND `role` = \'admin\'')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `users`
WHERE CAST(orgcode AS CHAR) = \'2345\'
AND `role` = \'admin\'')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `users`
WHERE CAST(orgcode AS CHAR) = :CAST(orgcode AS CHAR):
AND `role` = :role:', [...], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Controllers\DakoiiOrganizations.php(165): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiOrganizations->show('2345')
 8 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiOrganizations))
 9 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-13 08:28:49 --> [Caused by] mysqli_sql_exception: Unknown column 'orgcode' in 'where clause'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `users`
WHERE CAST(orgcode AS CHAR) = \'2345\'
AND `role` = \'admin\'', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `users`
WHERE CAST(orgcode AS CHAR) = \'2345\'
AND `role` = \'admin\'')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `users`
WHERE CAST(orgcode AS CHAR) = \'2345\'
AND `role` = \'admin\'')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `users`
WHERE CAST(orgcode AS CHAR) = :CAST(orgcode AS CHAR):
AND `role` = :role:', [...], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Controllers\DakoiiOrganizations.php(165): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiOrganizations->show('2345')
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiOrganizations))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-13 09:27:55 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'orgcode' in 'where clause'
[Method: GET, Route: dakoii/organizations/show/84261]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `users`
WHERE CAST(orgcode AS CHAR) = :CAST(orgcode AS CHAR):
AND `role` = :role:', [...], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Controllers\DakoiiOrganizations.php(165): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiOrganizations->show('84261')
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiOrganizations))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-13 09:27:55 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'orgcode' in 'where clause'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `users`
WHERE CAST(orgcode AS CHAR) = \'84261\'
AND `role` = \'admin\'')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `users`
WHERE CAST(orgcode AS CHAR) = \'84261\'
AND `role` = \'admin\'')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `users`
WHERE CAST(orgcode AS CHAR) = :CAST(orgcode AS CHAR):
AND `role` = :role:', [...], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Controllers\DakoiiOrganizations.php(165): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiOrganizations->show('84261')
 8 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiOrganizations))
 9 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-13 09:27:55 --> [Caused by] mysqli_sql_exception: Unknown column 'orgcode' in 'where clause'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `users`
WHERE CAST(orgcode AS CHAR) = \'84261\'
AND `role` = \'admin\'', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `users`
WHERE CAST(orgcode AS CHAR) = \'84261\'
AND `role` = \'admin\'')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `users`
WHERE CAST(orgcode AS CHAR) = \'84261\'
AND `role` = \'admin\'')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `users`
WHERE CAST(orgcode AS CHAR) = :CAST(orgcode AS CHAR):
AND `role` = :role:', [...], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Controllers\DakoiiOrganizations.php(165): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiOrganizations->show('84261')
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiOrganizations))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-13 02:42:08 --> Error: Class "App\Controllers\orgModel" not found
[Method: GET, Route: dakoii]
in APPPATH\Controllers\Dakoii.php on line 39.
 1 SYSTEMPATH\CodeIgniter.php(903): App\Controllers\Dakoii->__construct()
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-13 02:46:03 --> Error: Class "App\Models\orgModel" not found
[Method: GET, Route: dakoii/dashboard]
in APPPATH\Controllers\DakoiiDashboard.php on line 39.
 1 SYSTEMPATH\CodeIgniter.php(903): App\Controllers\DakoiiDashboard->__construct()
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-13 02:47:49 --> Error: Class "App\Models\SelectionModel" not found
[Method: GET, Route: dakoii/dashboard]
in APPPATH\Controllers\DakoiiDashboard.php on line 45.
 1 SYSTEMPATH\CodeIgniter.php(903): App\Controllers\DakoiiDashboard->__construct()
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-13 02:48:02 --> Error: Class "App\Models\SelectionModel" not found
[Method: GET, Route: dakoii/dashboard]
in APPPATH\Controllers\DakoiiDashboard.php on line 45.
 1 SYSTEMPATH\CodeIgniter.php(903): App\Controllers\DakoiiDashboard->__construct()
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-13 02:49:02 --> Error: Class "App\Models\SelectionModel" not found
[Method: GET, Route: dakoii/dashboard]
in APPPATH\Controllers\DakoiiDashboard.php on line 45.
 1 SYSTEMPATH\CodeIgniter.php(903): App\Controllers\DakoiiDashboard->__construct()
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-13 12:57:53 --> Error: Call to a member function orderBy() on null
[Method: GET, Route: dakoii/dashboard]
in APPPATH\Controllers\DakoiiDashboard.php on line 70.
 1 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiDashboard->index()
 2 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiDashboard))
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-13 03:59:58 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: dakoii/organizations/store]
in SYSTEMPATH\Security\Security.php on line 262.
 1 SYSTEMPATH\Security\Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH\Filters\CSRF.php(52): CodeIgniter\Security\Security->verify(Object(CodeIgniter\HTTP\IncomingRequest))
 3 SYSTEMPATH\Filters\Filters.php(241): CodeIgniter\Filters\CSRF->before(Object(CodeIgniter\HTTP\IncomingRequest), null)
 4 SYSTEMPATH\Filters\Filters.php(221): CodeIgniter\Filters\Filters->runBefore([...])
 5 SYSTEMPATH\CodeIgniter.php(479): CodeIgniter\Filters\Filters->run('dakoii/organizations/store', 'before')
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-13 14:04:48 --> ErrorException: Undefined array key "is_active"
[Method: GET, Route: dakoii/organizations/show/2]
in APPPATH\Views\dakoii\dakoii_organizations_show.php on line 151.
 1 APPPATH\Views\dakoii\dakoii_organizations_show.php(151): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "is_active"', 'C:\\xampp\\htdocs\\agristats\\app\\Views\\dakoii\\dakoii_organizations_show.php', 151)
 2 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\agristats\\app\\Views\\dakoii\\dakoii_organizations_show.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('dakoii/dakoii_organizations_show', [], true)
 5 APPPATH\Controllers\DakoiiOrganizations.php(171): view('dakoii/dakoii_organizations_show', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiOrganizations->show('2')
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiOrganizations))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-13 14:30:16 --> ErrorException: Undefined array key "orgcode"
[Method: GET, Route: dakoii/system-admins/edit/7]
in APPPATH\Views\dakoii\dakoii_system_admins_edit.php on line 59.
 1 APPPATH\Views\dakoii\dakoii_system_admins_edit.php(59): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "orgcode"', 'C:\\xampp\\htdocs\\agristats\\app\\Views\\dakoii\\dakoii_system_admins_edit.php', 59)
 2 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\agristats\\app\\Views\\dakoii\\dakoii_system_admins_edit.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('dakoii/dakoii_system_admins_edit', [], true)
 5 APPPATH\Controllers\DakoiiSystemAdmins.php(197): view('dakoii/dakoii_system_admins_edit', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiSystemAdmins->edit('7')
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiSystemAdmins))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-13 14:33:09 --> ErrorException: Undefined array key "orgcode"
[Method: GET, Route: dakoii/system-admins/edit/7]
in APPPATH\Views\dakoii\dakoii_system_admins_edit.php on line 59.
 1 APPPATH\Views\dakoii\dakoii_system_admins_edit.php(59): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "orgcode"', 'C:\\xampp\\htdocs\\agristats\\app\\Views\\dakoii\\dakoii_system_admins_edit.php', 59)
 2 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\agristats\\app\\Views\\dakoii\\dakoii_system_admins_edit.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('dakoii/dakoii_system_admins_edit', [], true)
 5 APPPATH\Controllers\DakoiiSystemAdmins.php(197): view('dakoii/dakoii_system_admins_edit', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiSystemAdmins->edit('7')
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiSystemAdmins))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-13 14:34:13 --> ErrorException: Undefined array key "orgcode"
[Method: GET, Route: dakoii/system-admins/edit/20]
in APPPATH\Views\dakoii\dakoii_system_admins_edit.php on line 59.
 1 APPPATH\Views\dakoii\dakoii_system_admins_edit.php(59): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "orgcode"', 'C:\\xampp\\htdocs\\agristats\\app\\Views\\dakoii\\dakoii_system_admins_edit.php', 59)
 2 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\agristats\\app\\Views\\dakoii\\dakoii_system_admins_edit.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('dakoii/dakoii_system_admins_edit', [], true)
 5 APPPATH\Controllers\DakoiiSystemAdmins.php(197): view('dakoii/dakoii_system_admins_edit', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiSystemAdmins->edit('20')
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiSystemAdmins))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-13 14:40:21 --> ErrorException: Undefined array key "orgcode"
[Method: GET, Route: dakoii/system-admins/edit/20]
in APPPATH\Views\dakoii\dakoii_system_admins_edit.php on line 59.
 1 APPPATH\Views\dakoii\dakoii_system_admins_edit.php(59): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "orgcode"', 'C:\\xampp\\htdocs\\agristats\\app\\Views\\dakoii\\dakoii_system_admins_edit.php', 59)
 2 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\agristats\\app\\Views\\dakoii\\dakoii_system_admins_edit.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('dakoii/dakoii_system_admins_edit', [], true)
 5 APPPATH\Controllers\DakoiiSystemAdmins.php(197): view('dakoii/dakoii_system_admins_edit', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiSystemAdmins->edit('20')
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiSystemAdmins))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-13 14:51:47 --> ErrorException: Undefined array key "is_active"
[Method: GET, Route: dakoii/system-admins/edit/20]
in APPPATH\Views\dakoii\dakoii_system_admins_edit.php on line 135.
 1 APPPATH\Views\dakoii\dakoii_system_admins_edit.php(135): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "is_active"', 'C:\\xampp\\htdocs\\agristats\\app\\Views\\dakoii\\dakoii_system_admins_edit.php', 135)
 2 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\agristats\\app\\Views\\dakoii\\dakoii_system_admins_edit.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('dakoii/dakoii_system_admins_edit', [], true)
 5 APPPATH\Controllers\DakoiiSystemAdmins.php(199): view('dakoii/dakoii_system_admins_edit', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiSystemAdmins->edit('20')
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiSystemAdmins))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
